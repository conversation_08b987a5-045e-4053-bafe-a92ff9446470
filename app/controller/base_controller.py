"""
基础 API 控制器
"""

from fastapi import APIRouter

from app.models.api.api_models import ApiInfoResponse, HealthResponse

router = APIRouter(tags=["基础"])


@router.get("/", response_model=ApiInfoResponse)
async def root():
    """根路径"""
    return ApiInfoResponse(message="作品分析 API", version="1.0.0")


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(status="healthy", message="服务运行正常")
