"""
作品分析相关的 API 控制器
"""

import logging

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from sqlmodel import Session

from app.db.database import get_session
from app.models.api import (
    AnalysisHistoryResponse,
    AnalysisRequest,
    AnalysisResponse,
    AnalysisTaskResponse,
)
from app.service import AwemeAnalysisService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analysis", tags=["作品分析"])


# 创建全局 service 实例，避免重复初始化
def get_aweme_analysis_service(
    session: Session = Depends(get_session),
) -> AwemeAnalysisService:
    """获取 AwemeAnalysisService 实例"""
    return AwemeAnalysisService(session)


@router.post("", response_model=AnalysisTaskResponse)
async def create_analysis_task(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks,
    service: AwemeAnalysisService = Depends(get_aweme_analysis_service),
):
    """
    分析关键词相关作品接口

    Args:
        request: 分析请求，包含关键词
        background_tasks: 后台任务
        service: AwemeAnalysisService 实例

    Returns:
        分析任务创建结果
    """
    try:
        logger.info(f"开始分析关键词: {request.keyword}")

        # 创建分析任务
        analysis_task = await service.search(request.keyword)

        # 后台执行分析任务
        background_tasks.add_task(service.analyze, analysis_task)

        # 返回结果（只包含 id 和 keyword）
        return AnalysisTaskResponse(
            id=analysis_task.id,
            keyword=analysis_task.keyword,
        )

    except Exception as e:
        logger.error(f"创建分析任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建分析任务失败: {str(e)}")


@router.get("/history", response_model=AnalysisHistoryResponse)
async def get_analysis_history(
    page: int = 1,
    page_size: int = 10,
    service: AwemeAnalysisService = Depends(get_aweme_analysis_service),
):
    """
    分页获取分析历史

    Args:
        page: 页码（从1开始）
        page_size: 每页数量
        service: AwemeAnalysisService 实例

    Returns:
        分页的分析历史列表
    """
    try:
        # 使用 service 层获取分页历史数据
        return service.get_analysis_history(page, page_size)

    except Exception as e:
        logger.error(f"获取分析历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析历史失败: {str(e)}")


@router.get("/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis_result(
    analysis_id: int,
    service: AwemeAnalysisService = Depends(get_aweme_analysis_service),
):
    """
    根据 ID 获取分析结果

    Args:
        analysis_id: 分析任务 ID
        service: AwemeAnalysisService 实例

    Returns:
        分析结果详情
    """
    try:
        # 使用 service 层获取分析结果
        result = service.get_by_id(analysis_id)

        if not result:
            raise HTTPException(status_code=404, detail="分析任务不存在")

        return AnalysisResponse(
            id=result.id,
            keyword=result.keyword,
            search_result=result.search_result,
            analysis_result=result.analysis_result,
            status=result.status,
            created_at=result.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分析结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析结果失败: {str(e)}")
