import logging
from datetime import datetime
from typing import Any, Dict

from sqlmodel import JSON, Column, Field, SQLModel

log = logging.getLogger(__name__)


class AwemeAnalysis(SQLModel, table=True):
    __tablename__ = "aweme_analysis"

    id: int | None = Field(default=None, primary_key=True, description="主键")
    keyword: str = Field(max_length=500, description="关键词")
    search_result: list[Dict[str, Any]] | None = Field(
        default=None, sa_column=Column(JSON), description="搜索结果"
    )
    analysis_result: Dict[str, Any] | None = Field(
        default=None, sa_column=Column(JSON), description="分析结果"
    )
    status: int = Field(default=0, description="状态：0 处理中 1 完成 2 失败")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
