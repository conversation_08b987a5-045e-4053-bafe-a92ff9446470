"""
视频裁剪工具模块
使用 MoviePy 实现多种视频裁剪功能
"""

import logging
import os
import tempfile
from typing import Union

from moviepy import VideoFileClip

logger = logging.getLogger(__name__)


def crop_video_by_time(
    video_path: str,
    start_time: float = 0,
    end_time: float | None = None,
    output_path: str | None = None,
) -> str:
    """
    按时间段裁剪视频

    Args:
        video_path: 原始视频文件路径
        start_time: 开始时间（秒），默认为0
        end_time: 结束时间（秒），如果为None则到视频结尾
        output_path: 输出文件路径，如果为None则生成临时文件

    Returns:
        str: 裁剪后视频的文件路径
    """
    # 如果没有指定输出路径，创建临时文件
    if output_path is None:
        temp_dir = tempfile.gettempdir()
        temp_filename = (
            f"cropped_video_{os.getpid()}_{int(start_time)}_{int(end_time or 0)}.mp4"
        )
        output_path = os.path.join(temp_dir, temp_filename)

    try:
        logger.info(f"正在加载视频文件: {video_path}")
        with VideoFileClip(video_path) as video:
            duration = video.duration
            logger.info(f"视频总时长: {duration:.2f} 秒")

            # 验证时间参数
            if start_time < 0:
                start_time = 0
            if start_time >= duration:
                raise ValueError(f"开始时间 {start_time} 超出视频时长 {duration}")

            # 如果没有指定结束时间，使用视频总时长
            if end_time is None:
                end_time = duration
            else:
                end_time = min(end_time, duration)

            if end_time <= start_time:
                raise ValueError(f"结束时间 {end_time} 必须大于开始时间 {start_time}")

            logger.info(f"裁剪时间段: {start_time:.2f}s - {end_time:.2f}s")

            # 裁剪视频
            clip = video.subclipped(start_time, end_time)

            # 保存裁剪后的视频
            logger.info(f"正在保存到: {output_path}")
            clip.write_videofile(
                output_path, codec="libx264", audio_codec="aac", logger=None
            )

        logger.info(f"视频裁剪完成: {output_path}")
        return output_path

    except Exception as e:
        # 如果出错且是临时文件，清理文件
        if (
            output_path
            and output_path.startswith(tempfile.gettempdir())
            and os.path.exists(output_path)
        ):
            try:
                os.remove(output_path)
            except:
                pass
        raise Exception(f"视频裁剪失败: {str(e)}")


def crop_video_by_percentage(
    video_path: str,
    start_percent: float = 0,
    end_percent: float = 100,
    output_path: str | None = None,
) -> str:
    """
    按百分比裁剪视频

    Args:
        video_path: 原始视频文件路径
        start_percent: 开始百分比（0-100），默认为0
        end_percent: 结束百分比（0-100），默认为100
        output_path: 输出文件路径，如果为None则生成临时文件

    Returns:
        str: 裁剪后视频的文件路径
    """
    if not (0 <= start_percent <= 100) or not (0 <= end_percent <= 100):
        raise ValueError("百分比必须在0-100之间")

    if start_percent >= end_percent:
        raise ValueError("结束百分比必须大于开始百分比")

    try:
        with VideoFileClip(video_path) as video:
            duration = video.duration
            start_time = duration * start_percent / 100
            end_time = duration * end_percent / 100

        return crop_video_by_time(video_path, start_time, end_time, output_path)

    except Exception as e:
        raise Exception(f"按百分比裁剪视频失败: {str(e)}")


def crop_video_by_frames(
    video_path: str,
    start_frame: int = 0,
    end_frame: int | None = None,
    output_path: str | None = None,
) -> str:
    """
    按帧数裁剪视频

    Args:
        video_path: 原始视频文件路径
        start_frame: 开始帧数，默认为0
        end_frame: 结束帧数，如果为None则到视频结尾
        output_path: 输出文件路径，如果为None则生成临时文件

    Returns:
        str: 裁剪后视频的文件路径
    """
    try:
        with VideoFileClip(video_path) as video:
            fps = video.fps
            total_frames = int(video.duration * fps)

            logger.info(f"视频帧率: {fps:.2f} fps, 总帧数: {total_frames}")

            # 验证帧数参数
            if start_frame < 0:
                start_frame = 0
            if start_frame >= total_frames:
                raise ValueError(f"开始帧 {start_frame} 超出总帧数 {total_frames}")

            if end_frame is None:
                end_frame = total_frames
            else:
                end_frame = min(end_frame, total_frames)

            if end_frame <= start_frame:
                raise ValueError(f"结束帧 {end_frame} 必须大于开始帧 {start_frame}")

            # 转换为时间
            start_time = start_frame / fps
            end_time = end_frame / fps

            logger.info(
                f"裁剪帧范围: {start_frame} - {end_frame} (时间: {start_time:.2f}s - {end_time:.2f}s)"
            )

        return crop_video_by_time(video_path, start_time, end_time, output_path)

    except Exception as e:
        raise Exception(f"按帧数裁剪视频失败: {str(e)}")


def crop_video(
    video_path: str,
    start: Union[int, float] = 0,
    end: Union[int, float] | None = None,
    mode: str = "time",
    output_path: str | None = None,
    **kwargs,
) -> str:
    """
    通用视频裁剪方法，支持多种裁剪模式

    Args:
        video_path: 原始视频文件路径
        start: 开始位置（根据mode不同含义不同）
        end: 结束位置（根据mode不同含义不同），None表示到结尾
        mode: 裁剪模式，支持 'time'（秒）、'percent'（百分比）、'frame'（帧数）
        output_path: 输出文件路径，如果为None则生成临时文件
        **kwargs: 其他参数

    Returns:
        str: 裁剪后视频的文件路径

    Examples:
        # 按时间裁剪：从第10秒到第30秒
        crop_video("input.mp4", start=10, end=30, mode="time")

        # 按百分比裁剪：从25%到75%
        crop_video("input.mp4", start=25, end=75, mode="percent")

        # 按帧数裁剪：从第100帧到第500帧
        crop_video("input.mp4", start=100, end=500, mode="frame")

        # 提取前5秒
        crop_video("input.mp4", start=0, end=5, mode="time")
    """
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"视频文件不存在: {video_path}")

    mode = mode.lower()

    if mode == "time":
        return crop_video_by_time(video_path, start, end, output_path)
    elif mode == "percent":
        return crop_video_by_percentage(video_path, start, end or 100, output_path)
    elif mode == "frame":
        return crop_video_by_frames(video_path, start, end, output_path)
    else:
        raise ValueError(
            f"不支持的裁剪模式: {mode}。支持的模式: 'time', 'percent', 'frame'"
        )


def get_video_info(video_path: str) -> dict:
    """
    获取视频基本信息

    Args:
        video_path: 视频文件路径

    Returns:
        dict: 包含视频信息的字典
    """
    try:
        with VideoFileClip(video_path) as video:
            info = {
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None,
                "total_frames": int(video.duration * video.fps) if video.fps else 0,
            }
            return info
    except Exception as e:
        raise Exception(f"获取视频信息失败: {str(e)}")
