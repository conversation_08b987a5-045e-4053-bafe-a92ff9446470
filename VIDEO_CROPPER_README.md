# 视频裁剪工具使用指南

本项目实现了一个基于 MoviePy 的视频裁剪工具，支持多种裁剪模式，包括按时间、按百分比和按帧数裁剪。

## 功能特性

- ✅ **按时间裁剪**: 指定开始和结束时间（秒）
- ✅ **按百分比裁剪**: 指定开始和结束百分比（0-100%）
- ✅ **按帧数裁剪**: 指定开始和结束帧数
- ✅ **通用接口**: 统一的 `crop_video()` 方法支持所有模式
- ✅ **自动临时文件**: 可选择输出路径或自动生成临时文件
- ✅ **视频信息获取**: 获取视频的基本信息（时长、分辨率、帧率等）
- ✅ **错误处理**: 完善的参数验证和错误处理

## 安装依赖

```bash
pip install moviepy
```

## 文件结构

```
app/
├── utils/
│   └── video_cropper.py      # 视频裁剪工具模块
├── agents/
│   └── analysis_agent.py     # 视频分析代理（集成裁剪功能）
test_video_cropper.py         # 测试脚本
```

## 使用方法

### 1. 导入模块

```python
from app.utils.video_cropper import (
    crop_video,
    crop_video_by_time,
    crop_video_by_percentage,
    crop_video_by_frames,
    get_video_info
)
```

### 2. 获取视频信息

```python
# 获取视频基本信息
info = get_video_info("input.mp4")
print(f"时长: {info['duration']:.2f} 秒")
print(f"分辨率: {info['width']}x{info['height']}")
print(f"帧率: {info['fps']:.2f} fps")
print(f"总帧数: {info['total_frames']}")
```

### 3. 按时间裁剪

```python
# 提取第10秒到第30秒的内容
output_path = crop_video_by_time(
    video_path="input.mp4",
    start_time=10,
    end_time=30,
    output_path="output_time.mp4"  # 可选，不指定则生成临时文件
)
```

### 4. 按百分比裁剪

```python
# 提取视频中间50%的内容（25%-75%）
output_path = crop_video_by_percentage(
    video_path="input.mp4",
    start_percent=25,
    end_percent=75,
    output_path="output_percent.mp4"
)
```

### 5. 按帧数裁剪

```python
# 提取第100帧到第500帧
output_path = crop_video_by_frames(
    video_path="input.mp4",
    start_frame=100,
    end_frame=500,
    output_path="output_frames.mp4"
)
```

### 6. 使用通用接口

```python
# 按时间裁剪
output_path = crop_video("input.mp4", start=10, end=30, mode="time")

# 按百分比裁剪
output_path = crop_video("input.mp4", start=25, end=75, mode="percent")

# 按帧数裁剪
output_path = crop_video("input.mp4", start=100, end=500, mode="frame")

# 提取前5秒
output_path = crop_video("input.mp4", start=0, end=5, mode="time")
```

## 运行测试

使用提供的测试脚本来验证功能：

```bash
# 使用默认视频文件
python test_video_cropper.py

# 指定视频文件
python test_video_cropper.py your_video.mp4

# 使用绝对路径
python test_video_cropper.py /path/to/your/video.mp4
```

## 参数说明

### crop_video() 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `video_path` | str | 必需 | 输入视频文件路径 |
| `start` | int/float | 0 | 开始位置（根据mode不同含义不同） |
| `end` | int/float/None | None | 结束位置，None表示到结尾 |
| `mode` | str | "time" | 裁剪模式："time"、"percent"、"frame" |
| `output_path` | str/None | None | 输出文件路径，None则生成临时文件 |

### 裁剪模式说明

- **time**: `start` 和 `end` 表示时间（秒）
- **percent**: `start` 和 `end` 表示百分比（0-100）
- **frame**: `start` 和 `end` 表示帧数

## 注意事项

1. **文件格式**: 输出文件默认为 MP4 格式，使用 H.264 视频编码和 AAC 音频编码
2. **临时文件**: 如果不指定输出路径，会在系统临时目录生成文件，使用完毕后建议手动清理
3. **参数验证**: 所有方法都包含参数验证，会检查时间、百分比、帧数的有效性
4. **错误处理**: 如果裁剪失败，临时文件会自动清理

## 错误处理

常见错误及解决方法：

- `FileNotFoundError`: 检查输入视频文件路径是否正确
- `ValueError`: 检查时间、百分比、帧数参数是否在有效范围内
- `Exception`: 检查视频文件是否损坏或格式不支持

## 集成到现有项目

视频裁剪功能已集成到 `analysis_agent.py` 中，可以与视频分析功能配合使用：

```python
from app.agents.analysis_agent import extract_first_5_seconds_moviepy

# 提取前5秒用于分析
temp_video = extract_first_5_seconds_moviepy("input.mp4")
```

## 性能优化建议

1. 对于大视频文件，建议先裁剪再进行其他处理
2. 使用临时文件时注意磁盘空间
3. 批量处理时可以考虑并行处理
4. 根据需要调整视频编码参数以平衡质量和文件大小
