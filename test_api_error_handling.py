#!/usr/bin/env python3
"""
测试 API 错误处理
"""

import asyncio
from fastapi.testclient import TestClient
from app.main import fastapi_app


def test_create_analysis_task_error():
    """测试创建分析任务的错误处理"""
    print("=== 测试创建分析任务错误处理 ===")
    
    # 创建测试客户端（不会触发 lifespan 事件）
    with TestClient(fastapi_app) as client:
        # 测试创建分析任务
        response = client.post(
            "/analysis",
            json={"keyword": "测试关键词"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 应该返回 500 错误，因为爬虫未初始化
        if response.status_code == 500:
            print("✅ 正确返回 500 错误")
            error_detail = response.json().get("detail", "")
            if "抖音爬虫未初始化" in error_detail:
                print("✅ 错误信息正确")
            else:
                print(f"❌ 错误信息不正确: {error_detail}")
        else:
            print(f"❌ 状态码不正确，期望 500，实际 {response.status_code}")


def test_get_analysis_history():
    """测试获取分析历史"""
    print("\n=== 测试获取分析历史 ===")
    
    with TestClient(fastapi_app) as client:
        response = client.get("/analysis/history?page=1&page_size=10")
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 历史查询应该正常工作，因为它不依赖爬虫
        if response.status_code == 200:
            print("✅ 历史查询正常工作")
            data = response.json()
            expected_keys = ["items", "total", "page", "page_size", "total_pages"]
            if all(key in data for key in expected_keys):
                print("✅ 响应格式正确")
            else:
                print(f"❌ 响应格式不正确，缺少字段: {set(expected_keys) - set(data.keys())}")
        else:
            print(f"❌ 状态码不正确，期望 200，实际 {response.status_code}")


def test_get_analysis_result():
    """测试获取分析结果"""
    print("\n=== 测试获取分析结果 ===")
    
    with TestClient(fastapi_app) as client:
        response = client.get("/analysis/999")  # 不存在的 ID
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 应该返回 404 错误
        if response.status_code == 404:
            print("✅ 正确返回 404 错误")
        else:
            print(f"❌ 状态码不正确，期望 404，实际 {response.status_code}")


if __name__ == "__main__":
    test_create_analysis_task_error()
    test_get_analysis_history()
    test_get_analysis_result()
    print("\n🎉 API 错误处理测试完成！")
