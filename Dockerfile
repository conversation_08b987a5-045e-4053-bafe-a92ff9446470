# 使用官方 Python 3.13 镜像
FROM python:3.13-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY pyproject.toml uv.lock ./
COPY app/ ./app/
COPY libs/ ./libs/
COPY run_server.py ./

# 安装 uv 包管理器
RUN pip install uv

# 安装 Python 依赖
RUN uv sync --frozen

# 暴露端口
EXPOSE 8000

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 启动命令
CMD ["python", "run_server.py"]
