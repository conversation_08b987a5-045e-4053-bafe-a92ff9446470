#!/usr/bin/env python3
"""
视频裁剪功能测试脚本
演示如何使用 moviepy 裁剪视频的各种方法
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.utils.video_cropper import (
    crop_video,
    crop_video_by_time,
    crop_video_by_percentage,
    crop_video_by_frames,
    get_video_info
)


def test_video_cropper(video_path: str):
    """
    测试视频裁剪功能
    
    Args:
        video_path: 测试视频文件路径
    """
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在: {video_path}")
        return
    
    print(f"测试视频: {video_path}")
    print("=" * 60)
    
    try:
        # 1. 获取视频信息
        print("1. 获取视频信息")
        info = get_video_info(video_path)
        print(f"   时长: {info['duration']:.2f} 秒")
        print(f"   分辨率: {info['width']}x{info['height']}")
        print(f"   帧率: {info['fps']:.2f} fps")
        print(f"   总帧数: {info['total_frames']}")
        print(f"   是否有音频: {info['has_audio']}")
        print()
        
        # 2. 按时间裁剪 - 提取前5秒
        print("2. 按时间裁剪 - 提取前5秒")
        try:
            output_path = crop_video_by_time(video_path, start_time=0, end_time=5)
            print(f"   成功: {output_path}")
        except Exception as e:
            print(f"   失败: {e}")
        print()
        
        # 3. 按百分比裁剪 - 提取中间50%
        print("3. 按百分比裁剪 - 提取中间50% (25%-75%)")
        try:
            output_path = crop_video_by_percentage(video_path, start_percent=25, end_percent=75)
            print(f"   成功: {output_path}")
        except Exception as e:
            print(f"   失败: {e}")
        print()
        
        # 4. 按帧数裁剪 - 提取前300帧
        print("4. 按帧数裁剪 - 提取前300帧")
        try:
            output_path = crop_video_by_frames(video_path, start_frame=0, end_frame=300)
            print(f"   成功: {output_path}")
        except Exception as e:
            print(f"   失败: {e}")
        print()
        
        # 5. 使用通用方法 - 按时间裁剪
        print("5. 使用通用方法 - 按时间裁剪 (10-20秒)")
        try:
            output_path = crop_video(video_path, start=10, end=20, mode="time")
            print(f"   成功: {output_path}")
        except Exception as e:
            print(f"   失败: {e}")
        print()
        
        # 6. 使用通用方法 - 按百分比裁剪
        print("6. 使用通用方法 - 按百分比裁剪 (前30%)")
        try:
            output_path = crop_video(video_path, start=0, end=30, mode="percent")
            print(f"   成功: {output_path}")
        except Exception as e:
            print(f"   失败: {e}")
        print()
        
        # 7. 使用通用方法 - 按帧数裁剪
        print("7. 使用通用方法 - 按帧数裁剪 (100-500帧)")
        try:
            output_path = crop_video(video_path, start=100, end=500, mode="frame")
            print(f"   成功: {output_path}")
        except Exception as e:
            print(f"   失败: {e}")
        print()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")


def main():
    """主函数"""
    print("视频裁剪功能测试")
    print("=" * 60)
    
    # 默认测试视频路径（请根据实际情况修改）
    default_video_path = "spring_mountain.mp4"
    
    # 从命令行参数获取视频路径
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        video_path = default_video_path
    
    # 如果是相对路径，转换为绝对路径
    if not os.path.isabs(video_path):
        video_path = os.path.join(project_root, video_path)
    
    test_video_cropper(video_path)
    
    print("\n测试完成!")
    print("\n使用方法:")
    print("  python test_video_cropper.py [视频文件路径]")
    print("\n示例:")
    print("  python test_video_cropper.py spring_mountain.mp4")
    print("  python test_video_cropper.py /path/to/your/video.mp4")


if __name__ == "__main__":
    main()
